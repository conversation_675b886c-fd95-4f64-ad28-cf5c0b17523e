---
# REQUIRED: The main title of the blog post.
title: "Standing Up a Demand Forecast in a Week"

# REQUIRED: A short, engaging subtitle for cards and sub-headings.
subtitle: "A practical guide from our UrbanHarvest experiment on how to decide with confidence when you have zero historical data."

# REQUIRED: URL for the main hero image at the top of the post.
heroImage: "https://images.unsplash.com/photo-1542744095-291d1f67b221?auto=format&fit=crop&w=2940&q=80"

# REQUIRED: Author details.
author:
  name: "<PERSON>"
  title: "Lead Data Scientist"
  avatar: "https://images.unsplash.com/photo-1633332755192-727a05c4013d?auto=format&fit=crop&w=100&h=100&q=80"

# REQUIRED: The publication date of the post. Your code expects this format.
publishedDate: "Sep 05, 2025"

# REQUIRED: Estimated time to read the article.
readTime: "5 min read"

# REQUIRED: An array of tags for filtering and display.
tags:
  - "Data Science"
  - "Validation"
---

One of the biggest hurdles for any new venture is the unknown. For 'UrbanHarvest,' a D2C grocery startup, the question was existential: which neighborhood should they expand into next? With five potential options but zero historical sales data, making the wrong choice could mean wasting tens of thousands of dollars on a failed launch. This is where rapid, intelligent experimentation comes in.

## The Challenge: Deciding Without Data

Traditional market research is slow and expensive. Surveys can be misleading, and demographic data alone doesn't capture intent. We needed to find a way to measure **actual demand** from real potential customers in each target neighborhood, and we needed to do it in under two weeks.

![A team collaborating around a whiteboard with charts and graphs.](https://images.unsplash.com/photo-1556742502-ec7c0e9f34b1?auto=format&fit=crop&w=1400&q=80)
_Data-driven decisions start with a clear hypothesis._

> The goal wasn't to be 100% certain. The goal was to be 75% confident, which is more than enough to de-risk a major business decision.

## Our Experiment: A Micro-Funnel Approach

We designed a simple, seven-day experiment. We built a micro-funnel combining hyper-local search ads on platforms like Google and Facebook with a simple waitlist landing page. The ads were targeted geographically to each of the five neighborhoods, with messaging tailored to local interests.

1.  **Define Success Metrics:** What would a 'successful' neighborhood look like?
2.  **Build Micro-Landing Pages:** Simple, focused pages for each neighborhood, capturing interest via a waitlist.
3.  **Hyper-Targeted Ad Campaigns:** Utilizing social media and search engines to reach specific geographic areas.
4.  **Monitor & Iterate:** Daily tracking of ad performance, sign-ups, and user feedback.
5.  **Analyze & Rank:** Comparing results across neighborhoods to identify the strongest contenders.

### Tools We Used

- **Google Ads:** For hyper-local search intent.
- **Facebook Ads:** For broader demographic targeting.
- **Mailchimp:** For waitlist management.
- **Google Analytics:** For tracking page performance.
- **Custom Python Script:** For data aggregation and initial demand modeling.

## The Outcome: Data-Backed Confidence

The result was a ranked list of all five neighborhoods, complete with projected CAC and a 75% confidence score. The startup was able to move forward with their expansion into the top-ranked area, saving an estimated $50,000 that would have been spent on a launch in the second-worst performing neighborhood. This entire process provided a reusable outcome—a playbook for all future expansion decisions.
