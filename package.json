{"name": "alphapebble", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"aos": "^2.3.4", "class-variance-authority": "^0.7.1", "gray-matter": "^4.0.3", "next": "15.5.2", "next-mdx-remote": "^5.0.0", "plaiceholder": "^3.0.0", "react": "19.1.0", "react-dom": "19.1.0", "rss": "^1.2.2", "sharp": "^0.34.3", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/aos": "^3.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/rss": "^0.0.32", "eslint": "^9", "eslint-config-next": "15.5.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "typescript": "^5"}}