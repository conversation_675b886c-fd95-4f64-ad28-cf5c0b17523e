@import "tailwindcss";

:root {
  color-scheme: dark;
  --mouse-x: 80%;
  --mouse-y: -10%;
}
@theme {
  --color-bg: #0b1220;
  --color-ink: #f6f6f7;
  --color-muted: #9aa3b2;
  --color-primary: #3b82f6;
  --color-primary-light: #60a5fa;
  --color-glass-bg: rgba(30, 41, 59, 0.3);
  --color-glass-border: rgba(255, 255, 255, 0.1);

  --font-sans: Inter, ui-sans-serif, system-ui;
}
@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  100% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}
@keyframes gradient {
  0% {
    background-position: 0 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}
@layer utilities {
  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  .animate-gradient {
    animation: gradient 8s ease infinite;
    background-size: 200% 200%;
  }
}
.cursor,
.cursor-dot {
  position: fixed;
  top: 0;
  pointer-events: none;
  left: 0;
}
.cursor,
.cursor-dot,
.modal-overlay {
  pointer-events: none;
}
.filter-btn,
.modal-close,
.text-muted {
  color: #9aa3b2;
}
@media (hover: hover) and (pointer: fine) {
  :not(a):not(button):not(input):not(textarea):not(select) {
    cursor: none !important;
  }
  a,
  button {
    cursor: pointer !important;
  }
  input,
  select,
  textarea {
    cursor: text !important;
  }
}
.cursor {
  width: 30px;
  height: 30px;
  border: 1px solid rgba(59, 130, 246, 0.8);
  border-radius: 50%;
  z-index: 9999;
  transition: 0.1s;
  backdrop-filter: blur(2px);
  background: rgba(59, 130, 246, 0.1);
  mix-blend-mode: screen;
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
}
.cursor-dot {
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  z-index: 10000;
  transition: transform 50ms;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.8);
}
.cursor.hover {
  width: 40px;
  height: 40px;
  background: rgba(59, 130, 246, 0.2);
  border: 4px solid #3b82f6;
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
}
.btn-ghost::before,
.btn-primary::before,
.glass::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  transition: left 0.5s;
  content: "";
}
body {
  background-color: #0b1220;
  background-image: radial-gradient(
    80rem 80rem at var(--mouse-x) var(--mouse-y),
    rgba(59, 130, 246, 0.12),
    transparent 55%
  );
  transition: background-position 0.8s ease-out;
  overflow-x: hidden;
}
@media (min-width: 768px) {
  section[id] {
    scroll-margin-top: 4rem;
  }
}
.emoji-heading {
  transition: 0.3s;
  display: inline-block;
}
.emoji-heading:hover {
  transform: scale(1.3) rotate(-15deg);
  filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.5));
}
.gtext {
  background: linear-gradient(45deg, #3b82f6, #60a5fa, #8b5cf6, #3b82f6);
  background-size: 400% 400%;
  animation: 8s infinite gradient;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
.glass {
  background: rgba(30, 41, 59, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  transition: 0.3s;
  position: relative;
  overflow: hidden;
}
.btn-ghost,
.pill {
  backdrop-filter: blur(10px);
  transition: 0.3s;
  position: relative;
  overflow: hidden;
}
.btn-ghost::before,
.glass::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}
.btn-ghost:hover::before,
.btn-primary:hover::before,
.glass:hover::before {
  left: 100%;
}
.glass:hover {
  border-color: rgba(59, 130, 246, 0.6);
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}
.btn-ghost:hover,
.pill:hover {
  border-color: rgba(59, 130, 246, 0.5);
}
.pill {
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 999px;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
}
.pill:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.2);
}
.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
  background-size: 200% 200%;
  transition: 0.3s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}
.btn-primary::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
}
.btn-primary:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 15px 35px rgba(59, 130, 246, 0.4),
    0 0 0 1px rgba(59, 130, 246, 0.5);
  background-position: 100% 100%;
}
.btn-ghost {
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.05);
}
.modal-content,
.modal-overlay {
  transition: 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
.btn-ghost:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.2);
}
.modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  background: rgba(11, 18, 32, 0.9);
  backdrop-filter: blur(12px);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
}
.modal-overlay.is-open {
  opacity: 1;
  pointer-events: auto;
}
.modal-content {
  width: 90%;
  max-width: 900px;
  height: 90%;
  background: linear-gradient(135deg, #1e293b, #334155);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1.5rem;
  padding: 1.5rem;
  transform: scale(0.9) translateY(20px);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}
.modal-overlay.is-open .modal-content {
  transform: scale(1) translateY(0);
}
.modal-close {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  transition: 0.3s;
  padding: 0.5rem;
  border-radius: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}
.modal-close:hover {
  color: #fff;
  transform: rotate(90deg) scale(1.1);
  background: rgba(239, 68, 68, 0.2);
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}
.gridlines {
  background-image:
    radial-gradient(
      circle at 1px 1px,
      rgba(59, 130, 246, 0.15) 1px,
      transparent 0
    ),
    radial-gradient(
      circle at 1px 1px,
      rgba(255, 255, 255, 0.05) 1px,
      transparent 0
    );
  background-size:
    32px 32px,
    64px 64px;
  background-position:
    0 0,
    16px 16px;
  mask-image: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.2),
    rgba(0, 0, 0, 0.8),
    rgba(0, 0, 0, 0.2)
  );
}
.header-glass {
  background: rgba(11, 18, 32, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}
.float-animation {
  animation: 6s ease-in-out infinite float;
}
.float-animation:nth-child(2n) {
  animation-delay: -2s;
}
.float-animation:nth-child(3n) {
  animation-delay: -4s;
}
.interactive-hover {
  transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.interactive-hover:hover {
  transform: translateY(-5px) scale(1.02);
  filter: brightness(1.1);
}
.enhanced-input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: 0.3s;
}
.enhanced-input:focus {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(255, 255, 255, 0.08);
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.1),
    0 4px 20px rgba(59, 130, 246, 0.2);
  outline: 0;
}
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa, #8b5cf6);
  z-index: 1000;
  transform-origin: left;
  transition: transform 0.1s;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(59, 130, 246, 0.2);
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: 1s linear infinite spin;
}
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}
::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 4px;
}
.filter-btn,
.tech-pill {
  background-color: rgba(255, 255, 255, 0.05);
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}
::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.8);
}
.skip-link {
  position: absolute;
  left: -999px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
}
.blog-hero,
.project-hero {
  background-size: cover;
  background-position: center;
  position: relative;
}
.skip-link:focus {
  left: 1rem;
  top: 1rem;
  width: auto;
  height: auto;
  padding: 0.5rem 0.75rem;
  background: #111;
  color: #fff;
  z-index: 9999;
  border-radius: 0.375rem;
}
.blog-card-image {
  aspect-ratio: 16/10;
  object-fit: cover;
  transition: transform 0.3s ease-in-out;
}
.interactive-hover:hover .blog-card-image,
.interactive-hover:hover .project-card-image {
  transform: scale(1.05);
}
.filter-btn {
  border-radius: 9999px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: 0.3s;
  cursor: pointer;
}
.filter-btn:hover {
  background-color: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
  color: #fff;
}
.filter-btn.active {
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.2);
  background-color: #3b82f6;
  color: #fff;
  border-color: #3b82f6;
  font-weight: 600;
  box-shadow:
    0 10px 25px -5px rgba(59, 130, 246, 0.2),
    0 8px 10px -6px rgba(59, 130, 246, 0.2);
}
.project-card-image {
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
  aspect-ratio: 16/9;
  object-fit: cover;
  transition: transform 0.3s ease-in-out;
}
.project-hero {
  color: #fff;
}
.project-hero::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    to top,
    #0b1220 0,
    rgba(11, 18, 32, 0.7) 30%,
    rgba(11, 18, 32, 0.5) 100%
  );
}
.content-section h2 {
  font-size: 2.25rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
}
.article-content p,
.content-section p {
  color: #9aa3b2;
  font-size: 1.125rem;
  line-height: 1.75;
  margin-bottom: 1rem;
}
.article-content strong,
.content-section strong {
  color: #f6f6f7;
}
.testimonial-card {
  margin-top: 4rem;
}
.testimonial-card .quote-icon {
  position: absolute;
  top: -1.5rem;
  left: -1rem;
  font-size: 8rem;
  color: rgba(59, 130, 246, 0.1);
  opacity: 0.8;
  line-height: 1;
}
.tech-pill {
  display: inline-block;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 9999px;
  color: #9aa3b2;
}
.blog-hero {
  color: #fff;
  padding: 6rem 0;
}
.blog-hero::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    to top,
    #10121b 0,
    rgba(16, 18, 27, 0.7) 30%,
    rgba(16, 18, 27, 0.5) 100%
  );
}
.article-content h2,
.article-content h3 {
  font-weight: 700;
  margin-top: 3rem;
  margin-bottom: 1rem;
  color: #f6f6f7;
}
.article-content h2 {
  font-size: 1.875rem;
}
.article-content h3 {
  font-size: 1.5rem;
}
.article-content a {
  color: #3b82f6;
  text-decoration: underline;
}
.article-content ol,
.article-content ul {
  color: #9aa3b2;
  font-size: 1.125rem;
  margin-left: 1.5rem;
  margin-bottom: 1.5rem;
}
.article-content ul {
  list-style-type: disc;
}
.article-content ol {
  list-style-type: decimal;
}
.article-content li {
  margin-bottom: 0.5rem;
  padding-left: 0.5rem;
}
.article-content blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  color: #9aa3b2;
  font-size: 1.25rem;
}
.toc-sidebar {
  position: sticky;
  top: 8rem;
  max-height: calc(100vh - 10rem);
  overflow-y: auto;
}
.toc-sidebar a {
  display: block;
  color: #9aa3b2;
  padding: 0.25rem 0;
  transition: color 0.2s;
}
.toc-sidebar a.active {
  color: #3b82f6;
  font-weight: 600;
}
.toc-sidebar a.h3 {
  padding-left: 1rem;
}
